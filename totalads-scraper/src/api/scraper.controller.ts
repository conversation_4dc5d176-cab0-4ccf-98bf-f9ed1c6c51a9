/**
 * Controller for scraper API endpoints
 */
import { Request, Response } from 'express';
import { expressAs<PERSON>Hand<PERSON> } from 'totalads-shared';
import { z } from 'zod';

import { scraperService } from '../core/scraper.service';
import { ErrorCodes, ScraperError } from '../utils/error-handler';
import logger from '../utils/logger';

// Validation schema for URL requests
export const ScrapeURLDataSchema = z.object({
	url: z.string().url({ message: "Invalid URL" }),
});

/**
 * Controller class for scraper API endpoints
 */
export class ScraperController {
	/**
	 * Scrape a URL and return the extracted data
	 */
	async scrapeUrl(req: Request, res: Response): Promise<void> {
		try {
			const { url } = req.body;

			logger.info(`API request received to scrape URL: ${url}`);
			const result = await scraperService.scrape(url);

			res.status(200).json({
				success: true,
				data: result,
			});
		} catch (error) {
			const scraperError = error as ScraperError;

			logger.error(
				`Error in scrape controller: ${scraperError.message}`,
				{
					code: scraperError.code,
					retryable: scraperError.retryable,
					statusCode: scraperError.statusCode,
					retryAfter: scraperError.retryAfter,
					url: req.body.url,
				},
			);

			// Determine appropriate HTTP status code
			let httpStatus = 500;
			let errorMessage =
				scraperError.message || "An error occurred during scraping";
			let retryAfter: number | undefined;

			if (scraperError instanceof ScraperError) {
				httpStatus = scraperError.statusCode || 500;
				retryAfter = scraperError.retryAfter;

				// Customize error messages for different error types
				switch (scraperError.code) {
					case ErrorCodes.TIMEOUT:
						errorMessage =
							"The website took too long to respond. This is common with large websites like Semrush.";
						break;
					case ErrorCodes.RATE_LIMITED:
						errorMessage =
							"Rate limit exceeded. Please wait before making another request.";
						break;
					case ErrorCodes.BLOCKED:
						errorMessage =
							"Access blocked by the website. This website may have anti-bot protection.";
						break;
					case ErrorCodes.LARGE_WEBSITE:
						errorMessage =
							"This large website requires special handling. Please try again in a few moments.";
						break;
					case ErrorCodes.FRAME_DETACHED:
						errorMessage =
							"Navigation was interrupted. This can happen with complex websites.";
						break;
					case ErrorCodes.NETWORK_ERROR:
						errorMessage =
							"Network connection error. Please check the URL and try again.";
						break;
					case ErrorCodes.CAPTCHA_DETECTED:
						errorMessage =
							"CAPTCHA detected. This website requires human verification.";
						break;
					default:
						errorMessage = scraperError.message;
				}
			}

			const responseBody: any = {
				success: false,
				error: errorMessage,
				code: scraperError.code,
				retryable: scraperError.retryable,
			};

			// Add retry-after header if specified
			if (retryAfter) {
				res.set("Retry-After", retryAfter.toString());
				responseBody.retryAfter = retryAfter;
			}

			res.status(httpStatus).json(responseBody);
		}
	}

	/**
	 * Health check endpoint to verify the scraper service is running
	 */
	async healthCheck(req: Request, res: Response): Promise<void> {
		res.status(200).json({
			status: "healthy",
			timestamp: new Date().toISOString(),
		});
	}
}

// Export a singleton instance
export const scraperController = new ScraperController();
export default scraperController;
