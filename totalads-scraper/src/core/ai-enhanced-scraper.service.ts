/**
 * AI-Enhanced Scraper Service
 * Integrates AI functionality for intelligent business data extraction
 */

import { Page } from 'puppeteer';
import { 
  aiService, 
  aiCostMonitor, 
  BusinessIntelligence, 
  EnhancedContactInfo, 
  AIProcessingResult 
} from 'totalads-shared';

import { BrowserTask, EnhancedScrapeResult, ScrapeResult } from '../models/scraper.model';
import { DataProcessorService } from '../processors/data-processor.service';
import { handleError } from '../utils/error-handler';
import logger from '../utils/logger';
import { BrowserController } from '../utils/scraper/browser-controller';
import { ContentExtractor } from '../utils/scraper/content-extractor';
import { TableHandler } from '../utils/scraper/table-handler';
import { extractEnhancedAboutData } from '../extractors/enhanced-scraper';

export interface AIEnhancedScrapeResult extends EnhancedScrapeResult {
  businessIntelligence: BusinessIntelligence;
  enhancedContactInfo: EnhancedContactInfo;
  extractedEntities: {
    people: string[];
    organizations: string[];
    locations: string[];
    products: string[];
  };
  sentiment: {
    overall: 'positive' | 'neutral' | 'negative';
    confidence: number;
  };
  aiProcessingTime: number;
  aiCost: number;
  recommendations: string[];
}

/**
 * AI-Enhanced Scraper Service with business intelligence extraction
 */
export class AIEnhancedScraperService {
  private browserController: BrowserController;
  private contentExtractor: ContentExtractor;
  private tableHandler: TableHandler;
  private dataProcessorService: DataProcessorService;

  constructor() {
    this.browserController = new BrowserController();
    this.contentExtractor = new ContentExtractor();
    this.tableHandler = new TableHandler();
    this.dataProcessorService = new DataProcessorService();
    
    logger.info('AI-Enhanced Scraper Service initialized');

    // Set up cost monitoring alerts
    aiCostMonitor.onAlert((alert) => {
      logger.warn(`AI Cost Alert: ${alert.message}`, {
        type: alert.type,
        severity: alert.severity,
        currentValue: alert.currentValue,
        threshold: alert.threshold
      });
    });
  }

  /**
   * Scrape and analyze website with AI enhancement
   */
  async scrapeWithAI(task: BrowserTask): Promise<AIEnhancedScrapeResult> {
    const startTime = Date.now();
    const url = task.url;

    try {
      logger.info(`Starting AI-enhanced scrape for: ${url}`);

      // Check if we can make AI requests within budget
      const budgetCheck = await aiCostMonitor.canMakeRequest();
      if (!budgetCheck.allowed) {
        logger.warn(`AI processing skipped due to budget limits: ${budgetCheck.reason}`);
        return this.getFallbackResult(url, budgetCheck.reason);
      }

      // Execute basic scraping first
      const basicResult = await this.executeBasicScraping(task);
      
      // Extract company name from title or URL for AI context
      const companyName = this.extractCompanyName(basicResult.title, url);
      
      // Prepare content for AI processing (optimize for cost)
      const optimizedContent = this.prepareContentForAI(basicResult.text);
      
      // Check content length and warn if it might be expensive
      const estimatedCost = this.estimateAICost(optimizedContent);
      logger.info(`Estimated AI processing cost: $${estimatedCost.toFixed(4)}`);

      // Final budget check with estimated cost
      const finalBudgetCheck = await aiCostMonitor.canMakeRequest(estimatedCost);
      if (!finalBudgetCheck.allowed) {
        logger.warn(`AI processing skipped due to estimated cost: ${finalBudgetCheck.reason}`);
        return this.getFallbackResult(url, finalBudgetCheck.reason, basicResult);
      }

      // Process with AI
      const aiStartTime = Date.now();
      const aiResult = await aiService.processBusinessAnalysis(
        optimizedContent,
        basicResult.contactDetails,
        companyName,
        url
      );
      const aiProcessingTime = Date.now() - aiStartTime;

      // Record AI usage for cost tracking
      aiCostMonitor.recordUsage(aiResult.usage.estimatedCost, aiResult.usage.totalTokens);

      // Get optimization recommendations
      const recommendations = aiCostMonitor.getOptimizationRecommendations();

      // Combine results
      const enhancedResult: AIEnhancedScrapeResult = {
        ...basicResult,
        url,
        timestamp: new Date(),
        processingTime: Date.now() - startTime,
        status: 'success',
        businessIntelligence: aiResult.businessIntelligence,
        enhancedContactInfo: aiResult.enhancedContactInfo,
        extractedEntities: aiResult.extractedEntities,
        sentiment: aiResult.sentiment,
        aiProcessingTime,
        aiCost: aiResult.usage.estimatedCost,
        recommendations
      };

      logger.info(`AI-enhanced scrape completed for ${url}`, {
        totalTime: enhancedResult.processingTime,
        aiTime: aiProcessingTime,
        aiCost: aiResult.usage.estimatedCost,
        confidence: aiResult.businessIntelligence.confidence
      });

      return enhancedResult;

    } catch (error) {
      const errorMessage = `Failed to perform AI-enhanced scrape for ${url}`;
      handleError(error as Error, 'AIEnhancedScraperService.scrapeWithAI');
      
      return {
        ...this.getDefaultScrapeResult(),
        url,
        timestamp: new Date(),
        processingTime: Date.now() - startTime,
        status: 'failed',
        error: (error as Error).message,
        businessIntelligence: this.getDefaultBusinessIntelligence(),
        enhancedContactInfo: this.getDefaultContactInfo(),
        extractedEntities: { people: [], organizations: [], locations: [], products: [] },
        sentiment: { overall: 'neutral', confidence: 0 },
        aiProcessingTime: 0,
        aiCost: 0,
        recommendations: []
      };
    }
  }

  /**
   * Execute basic scraping without AI
   */
  private async executeBasicScraping(task: BrowserTask): Promise<ScrapeResult> {
    return await this.browserController.executeWithPage<ScrapeResult>(
      task.url,
      async (page) => {
        // Extract basic metadata
        const metadata = await this.contentExtractor.getTitleAndDesc(page);
        
        // Extract links
        const links = await this.contentExtractor.extractLinks(page);
        
        // Extract and convert HTML to text
        const htmlText = await this.contentExtractor.convertHTMLToText(page);
        
        // Extract tables
        const tables = await this.tableHandler.extractTablesAndReplacePlaceholders(page);
        
        // Process text with tables
        const finalText = tables && tables.length > 0
          ? this.tableHandler.replaceTablePlaceholders(tables, htmlText)
          : htmlText;
        
        // Extract contact details
        const contactDetails = this.dataProcessorService.extractContactDetails(finalText);
        
        // Extract enhanced about data
        const aboutData = await extractEnhancedAboutData(page, task.url);
        
        return {
          title: metadata.title,
          desc: metadata.desc,
          nestedLinks: links,
          text: finalText,
          contactDetails,
          aboutData
        };
      }
    );
  }

  /**
   * Extract company name from title or URL
   */
  private extractCompanyName(title: string | null, url: string): string {
    if (title) {
      // Remove common suffixes and clean up
      const cleaned = title
        .replace(/\s*[-|–]\s*.*/g, '') // Remove everything after dash
        .replace(/\s*\|\s*.*/g, '')    // Remove everything after pipe
        .replace(/\s*(Home|Homepage|Welcome).*$/i, '') // Remove common page indicators
        .trim();
      
      if (cleaned.length > 0 && cleaned.length < 100) {
        return cleaned;
      }
    }
    
    // Fallback to domain name
    try {
      const domain = new URL(url).hostname.replace('www.', '');
      return domain.split('.')[0];
    } catch {
      return 'Unknown Company';
    }
  }

  /**
   * Prepare content for AI processing with cost optimization
   */
  private prepareContentForAI(content: string): string {
    // Remove excessive whitespace
    let optimized = content.replace(/\s+/g, ' ').trim();
    
    // Limit content length to control costs (max ~3000 chars for cost efficiency)
    const maxLength = 3000;
    if (optimized.length > maxLength) {
      // Try to cut at sentence boundaries
      const sentences = optimized.split(/[.!?]+/);
      let result = '';
      
      for (const sentence of sentences) {
        if (result.length + sentence.length > maxLength) {
          break;
        }
        result += sentence + '. ';
      }
      
      optimized = result.trim();
    }
    
    return optimized;
  }

  /**
   * Estimate AI processing cost
   */
  private estimateAICost(content: string): number {
    // Rough estimation: ~4 chars per token, multiple AI calls
    const estimatedTokens = Math.ceil(content.length / 4) * 3; // 3 AI calls
    return estimatedTokens * 0.000075; // Gemini Flash pricing
  }

  /**
   * Get fallback result when AI processing is skipped
   */
  private getFallbackResult(
    url: string, 
    reason: string, 
    basicResult?: ScrapeResult
  ): AIEnhancedScrapeResult {
    const base = basicResult || this.getDefaultScrapeResult();
    
    return {
      ...base,
      url,
      timestamp: new Date(),
      processingTime: 0,
      status: 'partial',
      error: `AI processing skipped: ${reason}`,
      businessIntelligence: this.getDefaultBusinessIntelligence(),
      enhancedContactInfo: this.getDefaultContactInfo(),
      extractedEntities: { people: [], organizations: [], locations: [], products: [] },
      sentiment: { overall: 'neutral', confidence: 0 },
      aiProcessingTime: 0,
      aiCost: 0,
      recommendations: ['AI processing was skipped due to budget constraints']
    };
  }

  /**
   * Get default scrape result
   */
  private getDefaultScrapeResult(): ScrapeResult {
    return {
      title: null,
      desc: null,
      nestedLinks: [],
      text: '',
      contactDetails: {},
      aboutData: {}
    };
  }

  /**
   * Get default business intelligence
   */
  private getDefaultBusinessIntelligence(): BusinessIntelligence {
    return {
      companyType: 'unknown',
      industry: [],
      businessModel: 'unknown',
      targetMarket: [],
      keyServices: [],
      competitiveAdvantages: [],
      marketPosition: 'unknown',
      technologies: [],
      partnerships: [],
      certifications: [],
      awards: [],
      socialPresence: { platforms: [], engagement: 'unknown' },
      riskFactors: [],
      opportunities: [],
      confidence: 0
    };
  }

  /**
   * Get default contact info
   */
  private getDefaultContactInfo(): EnhancedContactInfo {
    return {
      emails: [],
      phones: [],
      addresses: [],
      socialMedia: [],
      website: { primary: '', additional: [] }
    };
  }

  /**
   * Get cost statistics
   */
  getCostStats() {
    return aiCostMonitor.getUsageStats();
  }

  /**
   * Set AI budget
   */
  setAIBudget(budget: { daily?: number; monthly?: number; perRequest?: number }) {
    aiCostMonitor.setBudget(budget);
  }
}

// Export singleton instance
export const aiEnhancedScraperService = new AIEnhancedScraperService();
export default aiEnhancedScraperService;
