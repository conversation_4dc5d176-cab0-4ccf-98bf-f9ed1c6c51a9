/**
 * Test script for AI-enhanced scraper integration
 * Run this to verify the AI functionality is working correctly
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3000'; // Adjust port as needed
const TEST_URL = 'https://example.com'; // Test with a simple website

async function testAIIntegration() {
  console.log('🧪 Testing AI-Enhanced Scraper Integration\n');

  try {
    // Test 1: Health Check
    console.log('1️⃣ Testing Health Check...');
    const healthResponse = await axios.get(`${BASE_URL}/api/ai-scraper/health`);
    console.log('✅ Health Check:', healthResponse.data.status);
    console.log('   AI Enabled:', healthResponse.data.data.aiEnabled);
    console.log('');

    // Test 2: Cost Stats
    console.log('2️⃣ Testing Cost Statistics...');
    const costResponse = await axios.get(`${BASE_URL}/api/ai-scraper/cost-stats`);
    console.log('✅ Cost Stats Retrieved');
    console.log('   Today\'s Requests:', costResponse.data.data.today.requests);
    console.log('   Today\'s Cost: $', costResponse.data.data.today.cost.toFixed(4));
    console.log('');

    // Test 3: Update Budget
    console.log('3️⃣ Testing Budget Update...');
    const budgetResponse = await axios.put(`${BASE_URL}/api/ai-scraper/budget`, {
      daily: 5.0,
      monthly: 100.0,
      perRequest: 0.25
    });
    console.log('✅ Budget Updated:', budgetResponse.data.success);
    console.log('');

    // Test 4: AI-Enhanced Scraping
    console.log('4️⃣ Testing AI-Enhanced Scraping...');
    console.log(`   Scraping: ${TEST_URL}`);
    
    const scrapeResponse = await axios.post(`${BASE_URL}/api/ai-scraper/scrape`, {
      url: TEST_URL,
      options: {
        enableAI: true,
        aiOptions: {
          maxCost: 0.10,
          useCache: true,
          cacheExpiryMinutes: 60
        }
      }
    });

    if (scrapeResponse.data.success) {
      console.log('✅ AI-Enhanced Scraping Successful!');
      console.log('   Title:', scrapeResponse.data.data.title);
      console.log('   Processing Time:', scrapeResponse.data.data.processingTime, 'ms');
      console.log('   AI Processing Time:', scrapeResponse.data.data.aiProcessingTime, 'ms');
      console.log('   AI Cost: $', scrapeResponse.data.data.aiCost.toFixed(4));
      console.log('   Business Intelligence Confidence:', scrapeResponse.data.data.businessIntelligence.confidence, '%');
      console.log('   Industry:', scrapeResponse.data.data.businessIntelligence.industry.join(', '));
      console.log('   Market Position:', scrapeResponse.data.data.businessIntelligence.marketPosition);
      console.log('   Sentiment:', scrapeResponse.data.data.sentiment.overall);
    } else {
      console.log('❌ AI-Enhanced Scraping Failed:', scrapeResponse.data.error);
    }
    console.log('');

    // Test 5: Final Cost Check
    console.log('5️⃣ Final Cost Check...');
    const finalCostResponse = await axios.get(`${BASE_URL}/api/ai-scraper/cost-stats`);
    console.log('✅ Final Cost Stats');
    console.log('   Total Requests:', finalCostResponse.data.data.today.requests);
    console.log('   Total Cost: $', finalCostResponse.data.data.today.cost.toFixed(4));
    console.log('   Average Cost per Request: $', finalCostResponse.data.data.averages.costPerRequest.toFixed(4));
    console.log('');

    console.log('🎉 All tests completed successfully!');
    console.log('');
    console.log('📋 Summary:');
    console.log('   - AI services are operational');
    console.log('   - Cost monitoring is working');
    console.log('   - Budget controls are active');
    console.log('   - Business intelligence extraction is functional');
    console.log('');
    console.log('💡 Next Steps:');
    console.log('   1. Set your GEMINI_API_KEY in the environment');
    console.log('   2. Adjust budget limits as needed');
    console.log('   3. Test with your target websites');
    console.log('   4. Monitor costs and optimize as needed');

  } catch (error) {
    console.error('❌ Test Failed:', error.message);
    
    if (error.response) {
      console.error('   Status:', error.response.status);
      console.error('   Error:', error.response.data.error || error.response.data.message);
      
      if (error.response.status === 500 && error.response.data.message?.includes('GEMINI_API_KEY')) {
        console.log('');
        console.log('🔑 API Key Issue Detected:');
        console.log('   Please set your GEMINI_API_KEY environment variable');
        console.log('   1. Get your key from: https://makersuite.google.com/app/apikey');
        console.log('   2. Add to totalads-shared/.env: GEMINI_API_KEY=your_key_here');
        console.log('   3. Restart the server');
      }
    }
    
    console.log('');
    console.log('🔧 Troubleshooting:');
    console.log('   1. Ensure the server is running on the correct port');
    console.log('   2. Check that all dependencies are installed');
    console.log('   3. Verify environment variables are set');
    console.log('   4. Check server logs for detailed error messages');
  }
}

// Run the test
if (require.main === module) {
  testAIIntegration();
}

module.exports = { testAIIntegration };
