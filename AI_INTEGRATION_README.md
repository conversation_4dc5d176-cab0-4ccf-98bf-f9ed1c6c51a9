# AI-Enhanced Web Scraper Integration

This document describes the AI functionality added to your TotalAds scraper using **Gemini Flash Lite** for minimal cost and maximum business intelligence extraction.

## 🚀 Features Added

### 1. **AI-Enhanced Scraping Service**
- **Business Intelligence Extraction**: Company type, industry, business model, market position
- **Enhanced Contact Information**: Categorized emails, phones, addresses with confidence scores
- **Entity Extraction**: People, organizations, locations, products
- **Sentiment Analysis**: Overall sentiment with confidence scores
- **Cost Optimization**: Smart content filtering, caching, and budget controls

### 2. **Cost Management**
- **Real-time Budget Monitoring**: Daily, monthly, and per-request limits
- **Usage Tracking**: Detailed cost statistics and trends
- **Smart Caching**: Context-aware caching to reduce API calls
- **Content Optimization**: Intelligent content preprocessing to minimize tokens

### 3. **Business Intelligence**
- **Company Profiling**: Automated business type and industry classification
- **Market Analysis**: Target market and competitive advantage identification
- **Technology Stack**: Automatic detection of technologies used
- **Risk Assessment**: Identification of potential risks and opportunities

## 📁 File Structure

```
totalads-shared/
├── src/
│   ├── config/
│   │   └── ai.ts                    # AI client configuration
│   └── services/
│       ├── aiService.ts             # Main AI processing service
│       └── aiCostMonitor.ts         # Cost monitoring and budget management

totalads-scraper/
├── src/
│   ├── core/
│   │   └── ai-enhanced-scraper.service.ts  # AI-enhanced scraper
│   └── api/
│       ├── ai-enhanced-scraper.controller.ts  # REST API controller
│       └── ai-enhanced-routes.ts              # API routes
```

## 🔧 Setup Instructions

### 1. **Get Gemini API Key**
1. Go to [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Create a new API key
3. Copy the key for configuration

### 2. **Environment Configuration**
Create a `.env` file in `totalads-shared/` with:

```env
# Required
GEMINI_API_KEY=your_gemini_api_key_here

# Optional (with defaults)
AI_DAILY_BUDGET=10.0
AI_MONTHLY_BUDGET=200.0
AI_PER_REQUEST_LIMIT=0.50
AI_BUDGET_ENABLED=true
```

### 3. **Install Dependencies**
```bash
# In totalads-shared/
pnpm install

# Build shared package
pnpm run build

# In totalads-scraper/
pnpm install
```

### 4. **Start the Server**
```bash
cd totalads-scraper
pnpm run start:dev:server
```

## 🌐 API Endpoints

### **POST** `/api/ai-scraper/scrape`
Scrape a single URL with AI enhancement.

**Request:**
```json
{
  "url": "https://example.com",
  "options": {
    "enableAI": true,
    "aiOptions": {
      "maxCost": 0.10,
      "useCache": true,
      "cacheExpiryMinutes": 60
    }
  }
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "url": "https://example.com",
    "title": "Company Name",
    "businessIntelligence": {
      "companyType": "corporation",
      "industry": ["technology", "software"],
      "businessModel": "B2B",
      "marketPosition": "medium",
      "confidence": 85
    },
    "enhancedContactInfo": {
      "emails": [
        {
          "address": "<EMAIL>",
          "type": "general",
          "confidence": 90
        }
      ]
    },
    "sentiment": {
      "overall": "positive",
      "confidence": 78
    },
    "aiCost": 0.0234
  }
}
```

### **POST** `/api/ai-scraper/batch-scrape`
Batch process multiple URLs with cost control.

### **GET** `/api/ai-scraper/cost-stats`
Get current AI usage and cost statistics.

### **PUT** `/api/ai-scraper/budget`
Update AI budget settings.

### **GET** `/api/ai-scraper/health`
Health check for AI services.

## 💰 Cost Optimization Features

### 1. **Smart Content Preprocessing**
- Removes excessive whitespace and irrelevant content
- Prioritizes business-relevant text sections
- Limits content length to control token usage

### 2. **Intelligent Caching**
- Context-aware cache keys
- Configurable expiry times
- Automatic cache cleanup

### 3. **Budget Controls**
- Real-time cost estimation before API calls
- Automatic request blocking when limits exceeded
- Detailed usage tracking and alerts

### 4. **Batch Processing Optimization**
- Sequential processing to avoid rate limits
- Total cost limits for batch operations
- Automatic skipping when budget exceeded

## 📊 Business Intelligence Output

The AI extracts comprehensive business intelligence:

```typescript
interface BusinessIntelligence {
  companyType: string;           // corporation, llc, partnership, etc.
  industry: string[];            // technology, healthcare, finance, etc.
  businessModel: string;         // B2B, B2C, B2B2C, marketplace, etc.
  targetMarket: string[];        // SMB, enterprise, consumers, etc.
  keyServices: string[];         // main products/services
  competitiveAdvantages: string[]; // unique selling points
  marketPosition: 'startup' | 'small' | 'medium' | 'large' | 'enterprise';
  technologies: string[];        // tech stack used
  partnerships: string[];        // key partnerships
  certifications: string[];      // industry certifications
  awards: string[];             // recognition received
  socialPresence: {
    platforms: string[];         // social media platforms
    engagement: 'low' | 'medium' | 'high';
  };
  riskFactors: string[];         // potential risks
  opportunities: string[];       // growth opportunities
  confidence: number;            // 0-100 confidence score
}
```

## 🔍 Usage Examples

### Basic AI-Enhanced Scraping
```typescript
import { aiEnhancedScraperService } from './core/ai-enhanced-scraper.service';

const result = await aiEnhancedScraperService.scrapeWithAI({
  url: 'https://example.com',
  taskId: 'task-123'
});

console.log('Business Intelligence:', result.businessIntelligence);
console.log('AI Cost:', result.aiCost);
```

### Cost Monitoring
```typescript
import { aiCostMonitor } from 'totalads-shared';

// Set budget
aiCostMonitor.setBudget({
  daily: 5.0,
  monthly: 100.0,
  perRequest: 0.25
});

// Get usage stats
const stats = aiCostMonitor.getUsageStats();
console.log('Today\'s cost:', stats.today.cost);
```

## 🚨 Cost Alerts

The system automatically monitors costs and provides alerts:

- **Budget Warnings**: 80% of daily/monthly budget consumed
- **Budget Limits**: 95% of daily/monthly budget consumed
- **Rate Limiting**: Automatic request throttling when limits approached
- **Optimization Recommendations**: Suggestions to reduce costs

## 🔧 Configuration Options

### AI Client Configuration
```typescript
export const aiConfig = {
  model: 'gemini-1.5-flash',     // Gemini Flash Lite for cost efficiency
  maxTokens: 1000,               // Keep low for cost optimization
  temperature: 0.1,              // Low for consistent, factual outputs
  enableCaching: true,           // Enable response caching
  maxCacheSize: 1000,           // Cache up to 1000 responses
  costTrackingEnabled: true      // Enable cost tracking
};
```

### Budget Configuration
```typescript
const budget = {
  daily: 10.0,        // $10 daily limit
  monthly: 200.0,     // $200 monthly limit
  perRequest: 0.50,   // $0.50 per request limit
  enabled: true       // Enable budget enforcement
};
```

## 📈 Performance Metrics

The system tracks:
- **Processing Time**: Total and AI-specific processing time
- **Token Usage**: Input and output tokens consumed
- **Cost Tracking**: Real-time cost calculation
- **Cache Performance**: Hit rates and efficiency
- **Confidence Scores**: AI prediction confidence levels

## 🛠 Troubleshooting

### Common Issues

1. **API Key Not Set**
   - Ensure `GEMINI_API_KEY` is set in environment variables

2. **Budget Exceeded**
   - Check cost stats: `GET /api/ai-scraper/cost-stats`
   - Adjust budget: `PUT /api/ai-scraper/budget`

3. **Low Confidence Scores**
   - Content may be too generic or unclear
   - Try with more specific business content

4. **High Costs**
   - Enable caching with longer expiry times
   - Reduce content length in preprocessing
   - Lower per-request budget limits

## 🔮 Future Enhancements

Potential improvements:
- **LangGraph Integration**: For complex multi-step AI workflows
- **LangSmith Monitoring**: Advanced AI performance tracking
- **Custom Model Fine-tuning**: Domain-specific business intelligence
- **Real-time Streaming**: Progressive AI analysis results
- **Multi-language Support**: International business analysis

## 📞 Support

For questions or issues:
1. Check the logs for detailed error messages
2. Verify API key and budget settings
3. Review cost statistics for usage patterns
4. Test with the health check endpoint

The AI integration is designed to be cost-effective while providing maximum business value from your web scraping operations.
