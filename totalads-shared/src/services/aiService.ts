/**
 * AI Service for Business Intelligence Extraction
 * Optimized for scraping enhancement with minimal cost
 */

import { aiClient, TokenUsage } from '../config/ai';

export interface BusinessIntelligence {
	companyType: string;
	industry: string[];
	businessModel: string;
	targetMarket: string[];
	keyServices: string[];
	competitiveAdvantages: string[];
	marketPosition:
		| "startup"
		| "small"
		| "medium"
		| "large"
		| "enterprise"
		| "unknown";
	fundingStage?: string;
	revenue?: string;
	employeeCount?: string;
	technologies: string[];
	partnerships: string[];
	certifications: string[];
	awards: string[];
	socialPresence: {
		platforms: string[];
		engagement: "low" | "medium" | "high" | "unknown";
	};
	riskFactors: string[];
	opportunities: string[];
	confidence: number; // 0-100
}

export interface EnhancedContactInfo {
	emails: {
		address: string;
		type: "general" | "sales" | "support" | "hr" | "media" | "unknown";
		confidence: number;
	}[];
	phones: {
		number: string;
		type: "main" | "sales" | "support" | "mobile" | "fax" | "unknown";
		confidence: number;
	}[];
	addresses: {
		address: string;
		type: "headquarters" | "office" | "warehouse" | "store" | "unknown";
		confidence: number;
	}[];
	socialMedia: {
		platform: string;
		url: string;
		verified: boolean;
	}[];
	website: {
		primary: string;
		additional: string[];
	};
}

export interface AIProcessingResult {
	businessIntelligence: BusinessIntelligence;
	enhancedContactInfo: EnhancedContactInfo;
	extractedEntities: {
		people: string[];
		organizations: string[];
		locations: string[];
		products: string[];
	};
	sentiment: {
		overall: "positive" | "neutral" | "negative";
		confidence: number;
	};
	usage: TokenUsage;
	processingTime: number;
}

/**
 * AI Service for enhanced business data extraction
 */
export class AIService {
	private static instance: AIService;

	private constructor() {}

	public static getInstance(): AIService {
		if (!AIService.instance) {
			AIService.instance = new AIService();
		}
		return AIService.instance;
	}

	/**
	 * Extract business intelligence from scraped content
	 */
	async extractBusinessIntelligence(
		content: string,
		companyName?: string,
		websiteUrl?: string
	): Promise<BusinessIntelligence> {
		const startTime = Date.now();

		// Optimize content length to reduce token usage
		const optimizedContent = this.optimizeContentForAI(content, 2000);

		const prompt = `
Analyze the following company content and extract business intelligence. Return ONLY a JSON object with the specified structure.

Company: ${companyName || "Unknown"}
Website: ${websiteUrl || "Unknown"}

Required JSON structure:
{
  "companyType": "string (corporation, llc, partnership, etc.)",
  "industry": ["array of industry categories"],
  "businessModel": "string (B2B, B2C, B2B2C, marketplace, etc.)",
  "targetMarket": ["array of target markets"],
  "keyServices": ["array of main services/products"],
  "competitiveAdvantages": ["array of competitive advantages"],
  "marketPosition": "startup|small|medium|large|enterprise|unknown",
  "fundingStage": "string or null",
  "revenue": "string or null",
  "employeeCount": "string or null",
  "technologies": ["array of technologies used"],
  "partnerships": ["array of key partnerships"],
  "certifications": ["array of certifications"],
  "awards": ["array of awards/recognition"],
  "socialPresence": {
    "platforms": ["array of social platforms"],
    "engagement": "low|medium|high|unknown"
  },
  "riskFactors": ["array of potential risks"],
  "opportunities": ["array of opportunities"],
  "confidence": number (0-100)
}

Be concise and focus on factual information. If information is not available, use empty arrays or null values.
`;

		try {
			const result = await aiClient.generateResponse(
				prompt,
				optimizedContent,
				{
					useCache: true,
					cacheExpiryMinutes: 120, // Cache for 2 hours
				}
			);

			// Extract JSON from markdown code blocks if present
			const cleanedContent = this.extractJsonFromResponse(result.content);
			const businessIntelligence = JSON.parse(
				cleanedContent
			) as BusinessIntelligence;

			// Validate and sanitize the result
			return this.validateBusinessIntelligence(businessIntelligence);
		} catch (error) {
			console.error("Error extracting business intelligence:", error);
			return this.getDefaultBusinessIntelligence();
		}
	}

	/**
	 * Enhance contact information using AI
	 */
	async enhanceContactInfo(
		rawContactData: any,
		content: string
	): Promise<EnhancedContactInfo> {
		const optimizedContent = this.optimizeContentForAI(content, 1500);

		const prompt = `
Analyze the content and enhance the contact information. Return ONLY a JSON object.

Raw contact data: ${JSON.stringify(rawContactData)}

Required JSON structure:
{
  "emails": [{"address": "string", "type": "general|sales|support|hr|media|unknown", "confidence": number}],
  "phones": [{"number": "string", "type": "main|sales|support|mobile|fax|unknown", "confidence": number}],
  "addresses": [{"address": "string", "type": "headquarters|office|warehouse|store|unknown", "confidence": number}],
  "socialMedia": [{"platform": "string", "url": "string", "verified": boolean}],
  "website": {"primary": "string", "additional": ["array"]}
}

Classify contact types based on context. Confidence should be 0-100.
`;

		try {
			const result = await aiClient.generateResponse(
				prompt,
				optimizedContent,
				{
					useCache: true,
					cacheExpiryMinutes: 60,
				}
			);

			// Extract JSON from markdown code blocks if present
			const cleanedContent = this.extractJsonFromResponse(result.content);
			return JSON.parse(cleanedContent) as EnhancedContactInfo;
		} catch (error) {
			console.error("Error enhancing contact info:", error);
			return this.getDefaultContactInfo();
		}
	}

	/**
	 * Extract entities and sentiment from content
	 */
	async extractEntitiesAndSentiment(content: string): Promise<{
		entities: {
			people: string[];
			organizations: string[];
			locations: string[];
			products: string[];
		};
		sentiment: {
			overall: "positive" | "neutral" | "negative";
			confidence: number;
		};
	}> {
		const optimizedContent = this.optimizeContentForAI(content, 1000);

		const prompt = `
Extract entities and analyze sentiment from the content. Return ONLY a JSON object.

Required JSON structure:
{
  "entities": {
    "people": ["array of person names"],
    "organizations": ["array of organization names"],
    "locations": ["array of locations"],
    "products": ["array of products/services"]
  },
  "sentiment": {
    "overall": "positive|neutral|negative",
    "confidence": number (0-100)
  }
}

Focus on business-relevant entities. Limit each array to top 10 items.
`;

		try {
			const result = await aiClient.generateResponse(
				prompt,
				optimizedContent,
				{
					useCache: true,
					cacheExpiryMinutes: 30,
				}
			);

			// Extract JSON from markdown code blocks if present
			const cleanedContent = this.extractJsonFromResponse(result.content);
			return JSON.parse(cleanedContent);
		} catch (error) {
			console.error("Error extracting entities and sentiment:", error);
			return {
				entities: {
					people: [],
					organizations: [],
					locations: [],
					products: [],
				},
				sentiment: { overall: "neutral", confidence: 0 },
			};
		}
	}

	/**
	 * Process complete business analysis
	 */
	async processBusinessAnalysis(
		content: string,
		rawContactData: any,
		companyName?: string,
		websiteUrl?: string
	): Promise<AIProcessingResult> {
		const startTime = Date.now();

		try {
			// Run all AI analyses in parallel for efficiency
			const [
				businessIntelligence,
				enhancedContactInfo,
				entitiesAndSentiment,
			] = await Promise.all([
				this.extractBusinessIntelligence(
					content,
					companyName,
					websiteUrl
				),
				this.enhanceContactInfo(rawContactData, content),
				this.extractEntitiesAndSentiment(content),
			]);

			const processingTime = Date.now() - startTime;

			return {
				businessIntelligence,
				enhancedContactInfo,
				extractedEntities: entitiesAndSentiment.entities,
				sentiment: entitiesAndSentiment.sentiment,
				usage: {
					promptTokens: 0, // Will be calculated by aiClient
					completionTokens: 0,
					totalTokens: 0,
					estimatedCost: 0,
				},
				processingTime,
			};
		} catch (error) {
			console.error("Error in business analysis:", error);
			throw error;
		}
	}

	/**
	 * Extract JSON from AI response, handling markdown code blocks
	 */
	private extractJsonFromResponse(content: string): string {
		// Remove markdown code blocks if present
		const jsonMatch = content.match(/```(?:json)?\s*(\{[\s\S]*?\})\s*```/);
		if (jsonMatch) {
			return jsonMatch[1];
		}

		// If no code blocks, try to find JSON object
		const jsonObjectMatch = content.match(/\{[\s\S]*\}/);
		if (jsonObjectMatch) {
			return jsonObjectMatch[0];
		}

		// Return original content if no JSON found
		return content.trim();
	}

	/**
	 * Optimize content length for AI processing to reduce costs
	 */
	private optimizeContentForAI(content: string, maxLength: number): string {
		if (content.length <= maxLength) {
			return content;
		}

		// Extract most relevant parts
		const sentences = content.split(/[.!?]+/);
		const businessKeywords = [
			"company",
			"business",
			"service",
			"product",
			"industry",
			"market",
			"customer",
			"client",
			"solution",
			"technology",
			"innovation",
			"team",
			"about",
			"mission",
			"vision",
			"value",
			"experience",
		];

		// Score sentences based on business relevance
		const scoredSentences = sentences.map((sentence) => {
			const score = businessKeywords.reduce((acc, keyword) => {
				return acc + (sentence.toLowerCase().includes(keyword) ? 1 : 0);
			}, 0);
			return { sentence: sentence.trim(), score };
		});

		// Sort by score and take top sentences
		scoredSentences.sort((a, b) => b.score - a.score);

		let optimizedContent = "";
		for (const item of scoredSentences) {
			if (optimizedContent.length + item.sentence.length > maxLength) {
				break;
			}
			optimizedContent += item.sentence + ". ";
		}

		return optimizedContent.trim();
	}

	/**
	 * Validate and sanitize business intelligence data
	 */
	private validateBusinessIntelligence(data: any): BusinessIntelligence {
		return {
			companyType: data.companyType || "unknown",
			industry: Array.isArray(data.industry) ? data.industry : [],
			businessModel: data.businessModel || "unknown",
			targetMarket: Array.isArray(data.targetMarket)
				? data.targetMarket
				: [],
			keyServices: Array.isArray(data.keyServices)
				? data.keyServices
				: [],
			competitiveAdvantages: Array.isArray(data.competitiveAdvantages)
				? data.competitiveAdvantages
				: [],
			marketPosition: [
				"startup",
				"small",
				"medium",
				"large",
				"enterprise",
			].includes(data.marketPosition)
				? data.marketPosition
				: "unknown",
			fundingStage: data.fundingStage || null,
			revenue: data.revenue || null,
			employeeCount: data.employeeCount || null,
			technologies: Array.isArray(data.technologies)
				? data.technologies
				: [],
			partnerships: Array.isArray(data.partnerships)
				? data.partnerships
				: [],
			certifications: Array.isArray(data.certifications)
				? data.certifications
				: [],
			awards: Array.isArray(data.awards) ? data.awards : [],
			socialPresence: {
				platforms: Array.isArray(data.socialPresence?.platforms)
					? data.socialPresence.platforms
					: [],
				engagement: ["low", "medium", "high"].includes(
					data.socialPresence?.engagement
				)
					? data.socialPresence.engagement
					: "unknown",
			},
			riskFactors: Array.isArray(data.riskFactors)
				? data.riskFactors
				: [],
			opportunities: Array.isArray(data.opportunities)
				? data.opportunities
				: [],
			confidence:
				typeof data.confidence === "number"
					? Math.max(0, Math.min(100, data.confidence))
					: 0,
		};
	}

	/**
	 * Get default business intelligence structure
	 */
	private getDefaultBusinessIntelligence(): BusinessIntelligence {
		return {
			companyType: "unknown",
			industry: [],
			businessModel: "unknown",
			targetMarket: [],
			keyServices: [],
			competitiveAdvantages: [],
			marketPosition: "unknown",
			technologies: [],
			partnerships: [],
			certifications: [],
			awards: [],
			socialPresence: { platforms: [], engagement: "unknown" },
			riskFactors: [],
			opportunities: [],
			confidence: 0,
		};
	}

	/**
	 * Get default contact info structure
	 */
	private getDefaultContactInfo(): EnhancedContactInfo {
		return {
			emails: [],
			phones: [],
			addresses: [],
			socialMedia: [],
			website: { primary: "", additional: [] },
		};
	}
}

// Export singleton instance
export const aiService = AIService.getInstance();
export default aiService;
